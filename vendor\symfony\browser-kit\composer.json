{"name": "symfony/browser-kit", "type": "library", "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "keywords": [], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=8.2", "symfony/dom-crawler": "^6.4|^7.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}