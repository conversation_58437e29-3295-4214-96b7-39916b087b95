<?php

require_once 'vendor/autoload.php';

use Symfony\Component\BrowserKit\HttpBrowser;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\DomCrawler\Crawler;

class WebProxy
{
    private $browser;
    private $proxyUrl;
    private $targetUrl;

    public function __construct()
    {
        $this->browser = new HttpBrowser(HttpClient::create());
        $this->proxyUrl = $this->getCurrentProxyUrl();
    }

    public function handleRequest()
    {
        // Handle initiate parameter for setting up proxy session
        if (isset($_GET['initiate'])) {
            $this->handleInitiate($_GET['initiate']);
            return;
        }

        // Handle child domain requests
        if (isset($_GET['child']) && $_GET['child'] === 'true' && isset($_GET['url'])) {
            $this->handleChildDomain($_GET['url']);
            return;
        }

        // Get target URL from query parameter or construct from cookie + current path
        $this->targetUrl = $this->determineTargetUrl();

        if (empty($this->targetUrl)) {
            $this->showUsage();
            return;
        }

        // Validate URL
        if (!$this->isValidUrl($this->targetUrl)) {
            $this->showError('Invalid URL provided');
            return;
        }

        try {
            // Fetch the content
            $response = $this->fetchContent();

            // Process and output the content
            $this->processResponse($response);

        } catch (Exception $e) {
            $this->showError('Proxy error: ' . $e->getMessage());
        }
    }

    private function getCurrentProxyUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $script = $_SERVER['SCRIPT_NAME'];
        return $protocol . '://' . $host . $script;
    }

    private function isValidUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    private function handleInitiate($url)
    {
        // Validate the URL
        if (!$this->isValidUrl($url)) {
            $this->showError('Invalid URL provided');
            return;
        }

        $parsedUrl = parse_url($url);
        $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

        if (isset($parsedUrl['port'])) {
            $baseUrl .= ':' . $parsedUrl['port'];
        }

        // Store the main domain in cookies
        $this->storeDomainCookie($parsedUrl['host'], $baseUrl, true);

        // Extract the path and query from the original URL
        $path = $parsedUrl['path'] ?? '/';
        $query = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
        $fragment = isset($parsedUrl['fragment']) ? '#' . $parsedUrl['fragment'] : '';

        // Redirect to the clean proxy URL
        $redirectUrl = $this->getCurrentProxyUrl() . $path . $query . $fragment;
        header('Location: ' . $redirectUrl);
        exit;
    }

    private function handleChildDomain($url)
    {
        // Validate the URL
        if (!$this->isValidUrl($url)) {
            $this->showError('Invalid child domain URL provided');
            return;
        }

        $parsedUrl = parse_url($url);
        $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

        if (isset($parsedUrl['port'])) {
            $baseUrl .= ':' . $parsedUrl['port'];
        }

        // Store this domain in cookies for future reference
        $this->storeDomainCookie($parsedUrl['host'], $baseUrl, false);

        // Set the target URL and process normally
        $this->targetUrl = $url;

        try {
            // Fetch the content
            $response = $this->fetchContent();

            // Process and output the content
            $this->processResponse($response);

        } catch (Exception $e) {
            $this->showError('Child domain proxy error: ' . $e->getMessage());
        }
    }

    private function storeDomainCookie($domain, $baseUrl, $isMain = false)
    {
        if ($isMain) {
            // Store the main domain
            setcookie('proxy_base_url', $baseUrl, time() + (86400 * 30), '/'); // 30 days
            setcookie('proxy_main_domain', $domain, time() + (86400 * 30), '/'); // 30 days
        }

        // Get existing domains from cookie
        $existingDomains = [];
        if (isset($_COOKIE['proxy_domains'])) {
            $existingDomains = json_decode($_COOKIE['proxy_domains'], true) ?: [];
        }

        // Add this domain to the list
        $existingDomains[$domain] = $baseUrl;

        // Store updated domains list
        setcookie('proxy_domains', json_encode($existingDomains), time() + (86400 * 30), '/'); // 30 days
    }

    private function determineTargetUrl()
    {
        // First check if we have a direct URL parameter (legacy support)
        if (isset($_GET['url'])) {
            return $_GET['url'];
        }

        // Check if we have a base URL stored in cookie
        $baseUrl = $_COOKIE['proxy_base_url'] ?? '';
        if (empty($baseUrl)) {
            return '';
        }

        // Get the current request path and query
        $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '/';

        // Remove the script name from the request URI to get the path
        $path = str_replace($scriptName, '', $requestUri);
        if (empty($path) || $path === '/') {
            $path = '/';
        }

        // Construct the full target URL
        return $baseUrl . $path;
    }

    private function fetchContent()
    {
        // Set headers to mimic a real browser
        $headers = [
            'User-Agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0',
            'Accept' => $_SERVER['HTTP_ACCEPT'] ?? 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'en-US,en;q=0.5',
            'Accept-Encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? 'gzip, deflate',
            'Connection' => 'keep-alive',
        ];

        // Forward cookies if any, but exclude proxy-specific cookies
        if (!empty($_SERVER['HTTP_COOKIE'])) {
            $cookies = [];

            foreach ($_COOKIE as $name => $value) {
                // Skip proxy-specific cookies
                if (!in_array($name, ['proxy_base_url', 'proxy_main_domain', 'proxy_domains'])) {
                    // Forward all non-proxy cookies to maintain session state
                    $cookies[] = $name . '=' . $value;
                }
            }
            if (!empty($cookies)) {
                $headers['Cookie'] = implode('; ', $cookies);
            }
        }

        // Determine request method and handle POST data
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $postData = [];

        if ($method === 'POST') {
            $postData = $_POST;
        }

        // Make the request
        $this->browser->request($method, $this->targetUrl, $postData, [], $headers);

        return $this->browser->getResponse();
    }

    private function processResponse($response)
    {
        $content = $response->getContent();
        $contentType = $response->getHeader('Content-Type');

        // Set appropriate headers
        $this->setResponseHeaders($response);

        // Process content based on type
        if (strpos($contentType, 'text/html') !== false) {
            echo $this->processHtml($content);
        } elseif (strpos($contentType, 'text/css') !== false) {
            echo $this->processCss($content);
        } elseif (strpos($contentType, 'application/javascript') !== false ||
                  strpos($contentType, 'text/javascript') !== false ||
                  strpos($contentType, 'application/x-javascript') !== false) {
            echo $this->processJavaScript($content);
        } elseif (strpos($contentType, 'application/json') !== false) {
            // Process JSON that might contain URLs
            echo $this->processJson($content);
        } elseif (strpos($contentType, 'text/xml') !== false ||
                  strpos($contentType, 'application/xml') !== false) {
            // Process XML that might contain URLs
            echo $this->processXml($content);
        } else {
            // For images, fonts, and other binary content, output as-is
            echo $content;
        }
    }

    private function setResponseHeaders($response)
    {
        // Forward important headers
        $headersToForward = [
            'Content-Type',
            'Cache-Control',
            'Expires',
            'Last-Modified',
            'ETag',
        ];

        foreach ($headersToForward as $header) {
            $value = $response->getHeader($header);
            if (!empty($value)) {
                header($header . ': ' . $value);
            }
        }

        // Prevent caching for dynamic content
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Pragma: no-cache');
        header('Expires: Wed, 11 Jan 1984 05:00:00 GMT');
    }

    private function processHtml($html)
    {
        $crawler = new Crawler($html);

        // Inject proxy interceptor script first
        $this->injectProxyScript($crawler);

        // Process all links and resources
        $this->processLinks($crawler);
        $this->processScripts($crawler);
        $this->processStyles($crawler);
        $this->processImages($crawler);
        $this->processForms($crawler);
        $this->processIframes($crawler);
        $this->processMetaRefresh($crawler);
        $this->processMedia($crawler);
        $this->processObjects($crawler);
        $this->processPreloads($crawler);

        return $crawler->html();
    }

    private function injectProxyScript($crawler)
    {
        // Get proxy configuration for JavaScript
        $mainDomain = $_COOKIE['proxy_main_domain'] ?? '';
        $proxyUrl = $this->getCurrentProxyUrl();

        $script = $this->generateProxyScript($mainDomain, $proxyUrl);

        // Try to inject into head first, fallback to body
        $head = $crawler->filter('head');
        if ($head->count() > 0) {
            $headNode = $head->getNode(0);
            $scriptElement = $headNode->ownerDocument->createElement('script');
            $scriptElement->textContent = $script;
            $headNode->insertBefore($scriptElement, $headNode->firstChild);
        } else {
            // Fallback: inject at the beginning of body
            $body = $crawler->filter('body');
            if ($body->count() > 0) {
                $bodyNode = $body->getNode(0);
                $scriptElement = $bodyNode->ownerDocument->createElement('script');
                $scriptElement->textContent = $script;
                $bodyNode->insertBefore($scriptElement, $bodyNode->firstChild);
            }
        }
    }

    private function generateProxyScript($mainDomain, $proxyUrl)
    {
        return "
(function() {
    'use strict';

    // Proxy configuration
    const MAIN_DOMAIN = " . json_encode($mainDomain) . ";
    const PROXY_URL = " . json_encode($proxyUrl) . ";

    // Function to determine if URL should be proxied
    function shouldProxy(url) {
        if (!url || typeof url !== 'string') return false;
        if (url.startsWith('data:') || url.startsWith('blob:') || url.startsWith('javascript:')) return false;
        if (url.startsWith('#') || url.startsWith('mailto:') || url.startsWith('tel:')) return false;
        return true;
    }

    // Function to process JSON responses for URLs
    function processJsonResponse(jsonString) {
        try {
            const data = JSON.parse(jsonString);
            const processedData = processJsonData(data);
            return JSON.stringify(processedData);
        } catch (e) {
            return jsonString;
        }
    }

    // Function to recursively process JSON data
    function processJsonData(data) {
        if (typeof data === 'string' && shouldProxy(data)) {
            return proxyUrl(data);
        } else if (Array.isArray(data)) {
            return data.map(processJsonData);
        } else if (data && typeof data === 'object') {
            const processed = {};
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    // Common URL field names
                    if (['url', 'src', 'href', 'link', 'image', 'thumbnail', 'avatar', 'icon', 'poster', 'background'].includes(key.toLowerCase())) {
                        processed[key] = typeof data[key] === 'string' && shouldProxy(data[key]) ? proxyUrl(data[key]) : processJsonData(data[key]);
                    } else {
                        processed[key] = processJsonData(data[key]);
                    }
                }
            }
            return processed;
        }
        return data;
    }

    // Function to proxy a URL
    function proxyUrl(url) {
        if (!shouldProxy(url)) return url;

        try {
            let fullUrl = url;

            // Handle relative URLs
            if (!url.includes('://')) {
                if (url.startsWith('/')) {
                    fullUrl = window.location.protocol + '//' + MAIN_DOMAIN + url;
                } else {
                    const base = window.location.protocol + '//' + MAIN_DOMAIN + window.location.pathname;
                    fullUrl = new URL(url, base).href;
                }
            }

            const urlObj = new URL(fullUrl);

            // If same domain as main, use clean URL
            if (urlObj.hostname === MAIN_DOMAIN) {
                return PROXY_URL + urlObj.pathname + urlObj.search + urlObj.hash;
            } else {
                // Cross-domain, use child method
                return PROXY_URL + '?child=true&url=' + encodeURIComponent(fullUrl);
            }
        } catch (e) {
            console.warn('Proxy URL error:', e, url);
            return url;
        }
    }

    // Override XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        const proxiedUrl = proxyUrl(url);

        // Store original response handlers to process JSON responses
        const originalOnReadyStateChange = this.onreadystatechange;
        this.onreadystatechange = function() {
            if (this.readyState === 4 && this.status >= 200 && this.status < 300) {
                try {
                    const contentType = this.getResponseHeader('Content-Type') || '';
                    if (contentType.includes('application/json') || contentType.includes('text/json')) {
                        // Process JSON responses for URLs
                        const originalResponseText = this.responseText;
                        Object.defineProperty(this, 'responseText', {
                            get: function() {
                                return processJsonResponse(originalResponseText);
                            },
                            configurable: true
                        });
                    }
                } catch (e) {
                    // Ignore errors in response processing
                }
            }
            if (originalOnReadyStateChange) {
                return originalOnReadyStateChange.apply(this, arguments);
            }
        };

        return originalXHROpen.call(this, method, proxiedUrl, async, user, password);
    };

    // Override fetch
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        let url = input;
        if (input instanceof Request) {
            url = input.url;
        }
        const proxiedUrl = proxyUrl(url);

        let fetchPromise;
        if (input instanceof Request) {
            const newRequest = new Request(proxiedUrl, input);
            fetchPromise = originalFetch.call(this, newRequest, init);
        } else {
            fetchPromise = originalFetch.call(this, proxiedUrl, init);
        }

        // Process JSON responses
        return fetchPromise.then(function(response) {
            const contentType = response.headers.get('Content-Type') || '';
            if (contentType.includes('application/json') || contentType.includes('text/json')) {
                const originalJson = response.json;
                response.json = function() {
                    return originalJson.call(this).then(function(data) {
                        return processJsonData(data);
                    });
                };

                const originalText = response.text;
                response.text = function() {
                    return originalText.call(this).then(function(text) {
                        return processJsonResponse(text);
                    });
                };
            }
            return response;
        });
    };

    // Override dynamic script loading
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);

        if (tagName.toLowerCase() === 'script') {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if (name.toLowerCase() === 'src') {
                    value = proxyUrl(value);
                }
                return originalSetAttribute.call(this, name, value);
            };

            Object.defineProperty(element, 'src', {
                get: function() { return this.getAttribute('src'); },
                set: function(value) { this.setAttribute('src', proxyUrl(value)); }
            });
        }

        if (tagName.toLowerCase() === 'link') {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if (name.toLowerCase() === 'href') {
                    value = proxyUrl(value);
                }
                return originalSetAttribute.call(this, name, value);
            };

            Object.defineProperty(element, 'href', {
                get: function() { return this.getAttribute('href'); },
                set: function(value) { this.setAttribute('href', proxyUrl(value)); }
            });
        }

        // Handle all media elements (img, video, audio, source, track)
        if (['img', 'video', 'audio', 'source', 'track', 'embed', 'object', 'iframe'].includes(tagName.toLowerCase())) {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if (['src', 'data', 'poster'].includes(name.toLowerCase())) {
                    value = proxyUrl(value);
                }
                return originalSetAttribute.call(this, name, value);
            };

            // Override src property
            if (element.hasOwnProperty('src') || tagName.toLowerCase() !== 'object') {
                Object.defineProperty(element, 'src', {
                    get: function() { return this.getAttribute('src'); },
                    set: function(value) { this.setAttribute('src', proxyUrl(value)); }
                });
            }

            // Override data property for objects
            if (tagName.toLowerCase() === 'object') {
                Object.defineProperty(element, 'data', {
                    get: function() { return this.getAttribute('data'); },
                    set: function(value) { this.setAttribute('data', proxyUrl(value)); }
                });
            }

            // Override poster property for videos
            if (tagName.toLowerCase() === 'video') {
                Object.defineProperty(element, 'poster', {
                    get: function() { return this.getAttribute('poster'); },
                    set: function(value) { this.setAttribute('poster', proxyUrl(value)); }
                });
            }
        }

        return element;
    };

    // Override location changes
    const originalLocationAssign = window.location.assign;
    if (originalLocationAssign) {
        window.location.assign = function(url) {
            return originalLocationAssign.call(this, proxyUrl(url));
        };
    }

    const originalLocationReplace = window.location.replace;
    if (originalLocationReplace) {
        window.location.replace = function(url) {
            return originalLocationReplace.call(this, proxyUrl(url));
        };
    }

    // Override window.open
    const originalWindowOpen = window.open;
    window.open = function(url, name, features) {
        return originalWindowOpen.call(this, proxyUrl(url), name, features);
    };

    // Override common AJAX libraries if they exist
    if (window.jQuery && window.jQuery.ajax) {
        const originalJQueryAjax = window.jQuery.ajax;
        window.jQuery.ajax = function(options) {
            if (typeof options === 'string') {
                options = proxyUrl(options);
            } else if (options && options.url) {
                options.url = proxyUrl(options.url);
            }
            return originalJQueryAjax.call(this, options);
        };
    }

    if (window.axios) {
        const originalAxiosRequest = window.axios.request;
        window.axios.request = function(config) {
            if (config && config.url) {
                config.url = proxyUrl(config.url);
            }
            return originalAxiosRequest.call(this, config);
        };
    }

    // Monitor for dynamically added elements
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                    proxyElementUrls(node);

                    // Check nested elements
                    if (node.querySelectorAll) {
                        proxyNestedElements(node);
                    }
                }
            });
        });
    });

    // Function to proxy URLs in a single element
    function proxyElementUrls(element) {
        const tagName = element.tagName;
        if (!tagName) return;

        switch (tagName.toLowerCase()) {
            case 'script':
                if (element.src && shouldProxy(element.src)) {
                    element.src = proxyUrl(element.src);
                }
                break;
            case 'link':
                if (element.href && shouldProxy(element.href)) {
                    element.href = proxyUrl(element.href);
                }
                break;
            case 'img':
                if (element.src && shouldProxy(element.src)) {
                    element.src = proxyUrl(element.src);
                }
                if (element.srcset && shouldProxy(element.srcset)) {
                    element.srcset = proxySrcset(element.srcset);
                }
                break;
            case 'video':
            case 'audio':
                if (element.src && shouldProxy(element.src)) {
                    element.src = proxyUrl(element.src);
                }
                if (element.poster && shouldProxy(element.poster)) {
                    element.poster = proxyUrl(element.poster);
                }
                break;
            case 'source':
            case 'track':
                if (element.src && shouldProxy(element.src)) {
                    element.src = proxyUrl(element.src);
                }
                break;
            case 'embed':
            case 'iframe':
                if (element.src && shouldProxy(element.src)) {
                    element.src = proxyUrl(element.src);
                }
                break;
            case 'object':
                if (element.data && shouldProxy(element.data)) {
                    element.data = proxyUrl(element.data);
                }
                break;
        }
    }

    // Function to proxy URLs in nested elements
    function proxyNestedElements(container) {
        const selectors = [
            'script[src]', 'link[href]', 'img[src]', 'video[src]', 'audio[src]',
            'source[src]', 'track[src]', 'embed[src]', 'iframe[src]', 'object[data]',
            'video[poster]', 'img[srcset]'
        ];

        selectors.forEach(function(selector) {
            const elements = container.querySelectorAll(selector);
            elements.forEach(function(element) {
                proxyElementUrls(element);
            });
        });
    }

    // Function to proxy srcset attributes
    function proxySrcset(srcset) {
        return srcset.split(',').map(function(item) {
            const parts = item.trim().split(/\\s+/);
            if (parts[0] && shouldProxy(parts[0])) {
                parts[0] = proxyUrl(parts[0]);
            }
            return parts.join(' ');
        }).join(', ');
    }

    // Override CSS style setting to proxy background images and other CSS URLs
    const originalSetProperty = CSSStyleDeclaration.prototype.setProperty;
    CSSStyleDeclaration.prototype.setProperty = function(property, value, priority) {
        if (typeof value === 'string' && (property.includes('background') || property.includes('image'))) {
            value = value.replace(/url\\(\\s*['\"]?([^'\"\\)]+)['\"]?\\s*\\)/gi, function(match, url) {
                if (shouldProxy(url)) {
                    return 'url(' + proxyUrl(url) + ')';
                }
                return match;
            });
        }
        return originalSetProperty.call(this, property, value, priority);
    };

    // Override setAttribute for style attributes
    const originalSetAttribute = Element.prototype.setAttribute;
    Element.prototype.setAttribute = function(name, value) {
        if (name.toLowerCase() === 'style' && typeof value === 'string') {
            value = value.replace(/url\\(\\s*['\"]?([^'\"\\)]+)['\"]?\\s*\\)/gi, function(match, url) {
                if (shouldProxy(url)) {
                    return 'url(' + proxyUrl(url) + ')';
                }
                return match;
            });
        }
        return originalSetAttribute.call(this, name, value);
    };

    // Start observing
    observer.observe(document.documentElement, {
        childList: true,
        subtree: true
    });

    // Override Web Workers
    const originalWorker = window.Worker;
    if (originalWorker) {
        window.Worker = function(scriptURL, options) {
            return new originalWorker(proxyUrl(scriptURL), options);
        };
    }

    // Override SharedWorker
    const originalSharedWorker = window.SharedWorker;
    if (originalSharedWorker) {
        window.SharedWorker = function(scriptURL, options) {
            return new originalSharedWorker(proxyUrl(scriptURL), options);
        };
    }

    // Override ServiceWorker registration
    if (navigator.serviceWorker && navigator.serviceWorker.register) {
        const originalRegister = navigator.serviceWorker.register;
        navigator.serviceWorker.register = function(scriptURL, options) {
            return originalRegister.call(this, proxyUrl(scriptURL), options);
        };
    }

    // Override URL.createObjectURL for blob URLs (less common but thorough)
    const originalCreateObjectURL = URL.createObjectURL;
    if (originalCreateObjectURL) {
        URL.createObjectURL = function(object) {
            const url = originalCreateObjectURL.call(this, object);
            // Blob URLs are local, so we don't proxy them
            return url;
        };
    }

    // Override Image constructor
    const originalImage = window.Image;
    if (originalImage) {
        window.Image = function(width, height) {
            const img = new originalImage(width, height);
            const originalSrcSetter = Object.getOwnPropertyDescriptor(HTMLImageElement.prototype, 'src').set;
            Object.defineProperty(img, 'src', {
                get: function() { return this.getAttribute('src'); },
                set: function(value) {
                    originalSrcSetter.call(this, proxyUrl(value));
                }
            });
            return img;
        };
    }

    // Override Audio constructor
    const originalAudio = window.Audio;
    if (originalAudio) {
        window.Audio = function(src) {
            if (src) {
                return new originalAudio(proxyUrl(src));
            }
            return new originalAudio();
        };
    }

    console.log('Comprehensive proxy interceptor loaded for domain:', MAIN_DOMAIN);
    console.log('Intercepting: XHR, Fetch, Dynamic Elements, JSON, Media, CSS, Workers, Constructors');
})();
";
    }

    private function processLinks($crawler)
    {
        $crawler->filter('a[href]')->each(function ($node) {
            $href = $node->attr('href');
            if (!empty($href) && !$this->isExternalUrl($href)) {
                $proxiedUrl = $this->proxyUrl($href);
                $node->getNode(0)->setAttribute('href', $proxiedUrl);
            }
        });
    }

    private function processScripts($crawler)
    {
        $crawler->filter('script[src]')->each(function ($node) {
            $src = $node->attr('src');
            if (!empty($src) && !$this->isExternalUrl($src)) {
                $proxiedUrl = $this->proxyUrl($src);
                $node->getNode(0)->setAttribute('src', $proxiedUrl);
            }
        });

        // Process inline JavaScript
        $crawler->filter('script')->each(function ($node) {
            $content = $node->text();
            if (!empty($content)) {
                $processedContent = $this->processJavaScript($content);
                $node->getNode(0)->textContent = $processedContent;
            }
        });
    }

    private function processStyles($crawler)
    {
        $crawler->filter('link[rel="stylesheet"][href]')->each(function ($node) {
            $href = $node->attr('href');
            if (!empty($href) && !$this->isExternalUrl($href)) {
                $proxiedUrl = $this->proxyUrl($href);
                $node->getNode(0)->setAttribute('href', $proxiedUrl);
            }
        });

        // Process inline styles
        $crawler->filter('style')->each(function ($node) {
            $content = $node->text();
            if (!empty($content)) {
                $processedContent = $this->processCss($content);
                $node->getNode(0)->textContent = $processedContent;
            }
        });
    }

    private function processImages($crawler)
    {
        $crawler->filter('img[src]')->each(function ($node) {
            $src = $node->attr('src');
            if (!empty($src) && !$this->isExternalUrl($src)) {
                $proxiedUrl = $this->proxyUrl($src);
                $node->getNode(0)->setAttribute('src', $proxiedUrl);
            }
        });

        // Process other image-related attributes
        $crawler->filter('[srcset]')->each(function ($node) {
            $srcset = $node->attr('srcset');
            if (!empty($srcset)) {
                $processedSrcset = $this->processSrcset($srcset);
                $node->getNode(0)->setAttribute('srcset', $processedSrcset);
            }
        });
    }

    private function processForms($crawler)
    {
        $crawler->filter('form[action]')->each(function ($node) {
            $action = $node->attr('action');
            if (!empty($action) && !$this->isExternalUrl($action)) {
                $proxiedUrl = $this->proxyUrl($action);
                $node->getNode(0)->setAttribute('action', $proxiedUrl);
            }
        });
    }

    private function processIframes($crawler)
    {
        $crawler->filter('iframe[src]')->each(function ($node) {
            $src = $node->attr('src');
            if (!empty($src) && !$this->isExternalUrl($src)) {
                $proxiedUrl = $this->proxyUrl($src);
                $node->getNode(0)->setAttribute('src', $proxiedUrl);
            }
        });
    }

    private function processMetaRefresh($crawler)
    {
        $crawler->filter('meta[http-equiv="refresh"]')->each(function ($node) {
            $content = $node->attr('content');
            if (!empty($content) && preg_match('/url=(.+)/i', $content, $matches)) {
                $url = trim($matches[1]);
                if (!$this->isExternalUrl($url)) {
                    $proxiedUrl = $this->proxyUrl($url);
                    $newContent = preg_replace('/url=.+/i', 'url=' . $proxiedUrl, $content);
                    $node->getNode(0)->setAttribute('content', $newContent);
                }
            }
        });
    }

    private function processCss($css)
    {
        // Proxy URLs in CSS
        $patterns = [
            '/url\(\s*[\'"]?([^\'"\)]+)[\'"]?\s*\)/i',
            '/@import\s+[\'"]([^\'"]+)[\'"]/i',
        ];

        foreach ($patterns as $pattern) {
            $css = preg_replace_callback($pattern, function ($matches) {
                $url = trim($matches[1]);
                if (!$this->isExternalUrl($url)) {
                    $proxiedUrl = $this->proxyUrl($url);
                    return str_replace($matches[1], $proxiedUrl, $matches[0]);
                }
                return $matches[0];
            }, $css);
        }

        return $css;
    }

    private function processJavaScript($js)
    {
        // Proxy URLs in JavaScript
        $patterns = [
            '/(?:window\.location|location\.href)\s*=\s*[\'"]([^\'"]+)[\'"]/i',
            '/\.src\s*=\s*[\'"]([^\'"]+)[\'"]/i',
            '/\.href\s*=\s*[\'"]([^\'"]+)[\'"]/i',
            '/fetch\(\s*[\'"]([^\'"]+)[\'"]\s*[,\)]/i',
            '/fetch\(\s*`([^`]+)`\s*[,\)]/i',
            '/XMLHttpRequest\.open\(\s*[\'"](?:GET|POST|PUT|DELETE)[\'"]\s*,\s*[\'"]([^\'"]+)[\'"]/i',
            '/axios\.(?:get|post|put|delete|patch)\(\s*[\'"]([^\'"]+)[\'"]/i',
            '/\$\.(?:get|post|ajax)\(\s*[\'"]([^\'"]+)[\'"]/i',
            '/new\s+URL\(\s*[\'"]([^\'"]+)[\'"]/i',
            '/location\.assign\(\s*[\'"]([^\'"]+)[\'"]/i',
            '/location\.replace\(\s*[\'"]([^\'"]+)[\'"]/i',
            '/window\.open\(\s*[\'"]([^\'"]+)[\'"]/i',
        ];

        foreach ($patterns as $pattern) {
            $js = preg_replace_callback($pattern, function ($matches) {
                $url = trim($matches[1]);
                if (!$this->isExternalUrl($url)) {
                    $proxiedUrl = $this->proxyUrl($url);
                    return str_replace($matches[1], $proxiedUrl, $matches[0]);
                }
                return $matches[0];
            }, $js);
        }

        return $js;
    }

    private function processJson($json)
    {
        // Process JSON that might contain URLs
        $patterns = [
            '/"url"\s*:\s*"([^"]+)"/i',
            '/"href"\s*:\s*"([^"]+)"/i',
            '/"src"\s*:\s*"([^"]+)"/i',
            '/"link"\s*:\s*"([^"]+)"/i',
        ];

        foreach ($patterns as $pattern) {
            $json = preg_replace_callback($pattern, function ($matches) {
                $url = trim($matches[1]);
                if (!$this->isExternalUrl($url)) {
                    $proxiedUrl = $this->proxyUrl($url);
                    return str_replace($matches[1], $proxiedUrl, $matches[0]);
                }
                return $matches[0];
            }, $json);
        }

        return $json;
    }

    private function processXml($xml)
    {
        // Process XML that might contain URLs in attributes
        $patterns = [
            '/href\s*=\s*[\'"]([^\'"]+)[\'"]/i',
            '/src\s*=\s*[\'"]([^\'"]+)[\'"]/i',
            '/url\s*=\s*[\'"]([^\'"]+)[\'"]/i',
        ];

        foreach ($patterns as $pattern) {
            $xml = preg_replace_callback($pattern, function ($matches) {
                $url = trim($matches[1]);
                if (!$this->isExternalUrl($url)) {
                    $proxiedUrl = $this->proxyUrl($url);
                    return str_replace($matches[1], $proxiedUrl, $matches[0]);
                }
                return $matches[0];
            }, $xml);
        }

        return $xml;
    }

    private function processMedia($crawler)
    {
        // Process video and audio elements
        $crawler->filter('video[src], audio[src]')->each(function ($node) {
            $src = $node->attr('src');
            if (!empty($src) && !$this->isExternalUrl($src)) {
                $proxiedUrl = $this->proxyUrl($src);
                $node->getNode(0)->setAttribute('src', $proxiedUrl);
            }
        });

        // Process source elements within video/audio
        $crawler->filter('source[src]')->each(function ($node) {
            $src = $node->attr('src');
            if (!empty($src) && !$this->isExternalUrl($src)) {
                $proxiedUrl = $this->proxyUrl($src);
                $node->getNode(0)->setAttribute('src', $proxiedUrl);
            }
        });
    }

    private function processObjects($crawler)
    {
        // Process object and embed elements
        $crawler->filter('object[data]')->each(function ($node) {
            $data = $node->attr('data');
            if (!empty($data) && !$this->isExternalUrl($data)) {
                $proxiedUrl = $this->proxyUrl($data);
                $node->getNode(0)->setAttribute('data', $proxiedUrl);
            }
        });

        $crawler->filter('embed[src]')->each(function ($node) {
            $src = $node->attr('src');
            if (!empty($src) && !$this->isExternalUrl($src)) {
                $proxiedUrl = $this->proxyUrl($src);
                $node->getNode(0)->setAttribute('src', $proxiedUrl);
            }
        });
    }

    private function processPreloads($crawler)
    {
        // Process link preload elements
        $crawler->filter('link[rel="preload"][href], link[rel="prefetch"][href], link[rel="dns-prefetch"][href]')->each(function ($node) {
            $href = $node->attr('href');
            if (!empty($href) && !$this->isExternalUrl($href)) {
                $proxiedUrl = $this->proxyUrl($href);
                $node->getNode(0)->setAttribute('href', $proxiedUrl);
            }
        });
    }

    private function processSrcset($srcset)
    {
        $items = explode(',', $srcset);
        $processedItems = [];

        foreach ($items as $item) {
            $parts = trim($item);
            if (preg_match('/^(\S+)\s*(\d+[wx])?$/', $parts, $matches)) {
                $url = $matches[1];
                $descriptor = $matches[2] ?? '';

                if (!$this->isExternalUrl($url)) {
                    $proxiedUrl = $this->proxyUrl($url);
                    $processedItems[] = $proxiedUrl . ($descriptor ? ' ' . $descriptor : '');
                } else {
                    $processedItems[] = $parts;
                }
            } else {
                $processedItems[] = $parts;
            }
        }

        return implode(', ', $processedItems);
    }

    private function proxyUrl($url)
    {
        // Handle relative URLs
        if (strpos($url, '://') === false) {
            $baseUrl = $this->targetUrl;
            $parsedUrl = parse_url($baseUrl);
            $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

            if (isset($parsedUrl['port'])) {
                $baseUrl .= ':' . $parsedUrl['port'];
            }

            if (strpos($url, '/') === 0) {
                // Absolute path
                $url = $baseUrl . $url;
            } else {
                // Relative path
                $path = dirname($parsedUrl['path'] ?? '/');
                $url = $baseUrl . $path . '/' . $url;
            }
        }

        // Parse the final URL to determine domain
        $parsedUrl = parse_url($url);
        $urlDomain = $parsedUrl['host'] ?? '';

        // Get main domain from cookie
        $mainDomain = $_COOKIE['proxy_main_domain'] ?? '';
        $cookieBaseUrl = $_COOKIE['proxy_base_url'] ?? '';

        // Check if we're using cookie-based session
        if (!empty($cookieBaseUrl) && !empty($mainDomain)) {
            // If this URL is from the main domain, use clean URLs
            if ($urlDomain === $mainDomain) {
                $path = $parsedUrl['path'] ?? '/';
                $query = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
                $fragment = isset($parsedUrl['fragment']) ? '#' . $parsedUrl['fragment'] : '';

                return $this->getCurrentProxyUrl() . $path . $query . $fragment;
            } else {
                // This is a cross-domain asset, use child domain method
                return $this->getCurrentProxyUrl() . '?child=true&url=' . urlencode($url);
            }
        }

        // Fallback to old method
        return $this->proxyUrl . '?url=' . urlencode($url);
    }

    private function isExternalUrl($url)
    {
        // Check if URL is external (different domain or protocol)
        if (strpos($url, '://') === false) {
            return false; // Relative URL
        }

        $urlDomain = parse_url($url, PHP_URL_HOST);

        // Get main domain from cookie
        $mainDomain = $_COOKIE['proxy_main_domain'] ?? '';

        // If we have a main domain set, check against it
        if (!empty($mainDomain)) {
            return $urlDomain !== $mainDomain;
        }

        // Fallback to checking against current target URL
        $targetDomain = parse_url($this->targetUrl, PHP_URL_HOST);
        return $targetDomain !== $urlDomain;
    }

    private function showUsage()
    {
        echo '<!DOCTYPE html>
<html>
<head>
    <title>Web Proxy</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { border: 1px solid #ddd; padding: 30px; border-radius: 5px; margin-bottom: 20px; }
        h1 { color: #333; }
        h2 { color: #555; font-size: 1.2em; }
        input[type="url"] { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 3px; }
        button { background-color: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background-color: #005a87; }
        .method { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .recommended { border-left: 4px solid #007cba; }
        code { background-color: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container recommended">
        <h1>Web Proxy</h1>
        <h2>🚀 Recommended: Session-Based Browsing</h2>
        <p>For the best experience, use the <code>initiate</code> parameter to start a session:</p>
        <form method="get">
            <input type="url" name="initiate" placeholder="https://youtube.com/watch?v=example" required>
            <button type="submit">Start Session</button>
        </form>
        <p><small>This will create a clean browsing session where URLs look natural (e.g., <code>/watch?v=example</code>)</small></p>
    </div>

    <div class="container">
        <h2>Legacy Method</h2>
        <p>Direct URL proxying (old method):</p>
        <form method="get">
            <input type="url" name="url" placeholder="https://example.com" required>
            <button type="submit">Proxy URL</button>
        </form>
    </div>

    <div class="method">
        <h2>🔧 How it works:</h2>
        <p><strong>Session-based:</strong> Visit <code>/?initiate=https://youtube.com/watch?v=example</code> → Redirects to <code>/watch?v=example</code></p>
        <p><strong>Multi-domain:</strong> Main domain assets use clean URLs, cross-domain assets use <code>/?child=true&url=...</code></p>
        <p><strong>Legacy:</strong> Visit <code>/?url=https://example.com</code> → Shows proxied content</p>
    </div>

    <div class="method">
        <h2>🍪 Smart Cookie Management:</h2>
        <p><strong>Main Domain:</strong> <code>youtube.com/style.css</code> → <code>/style.css</code></p>
        <p><strong>Cross-Domain:</strong> <code>fonts.googleapis.com/font.woff</code> → <code>/?child=true&url=https://fonts.googleapis.com/font.woff</code></p>
        <p><strong>Cookies:</strong> Automatically saves and manages cookies for all domains encountered</p>
    </div>
</body>
</html>';
    }

    private function showError($message)
    {
        echo '<!DOCTYPE html>
<html>
<head>
    <title>Proxy Error</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .error { border: 1px solid #dc3232; background-color: #fbeaea; padding: 20px; border-radius: 5px; color: #dc3232; }
    </style>
</head>
<body>
    <div class="error">
        <h2>Error</h2>
        <p>' . htmlspecialchars($message) . '</p>
        <p><a href="' . htmlspecialchars($this->proxyUrl) . '">← Back to proxy</a></p>
    </div>
</body>
</html>';
    }
}

// Handle the request
$proxy = new WebProxy();
$proxy->handleRequest();
